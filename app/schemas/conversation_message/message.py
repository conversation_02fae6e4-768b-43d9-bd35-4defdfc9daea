from datetime import datetime
from typing import Annotated, Any, Sequence
from uuid import UUID

from fastapi import UploadFile
from pydantic import ConfigDict, Field, field_validator

from constants.message import (
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    OptionType,
    PageType,
    SuggestedUserPrompt,
    SystemReplyType,
)
from core import json
from core.schemas import CustomModel
from validators import validate_files_core, validate_value_as_list_of_strings

from ..document import DocumentResponse
from .option import Option


__all__ = [
    'ConversationMessageIntentClassifierServiceResponse',
    'ConversationMessageProcessingResult',
    'MessageValidator',
    'BaseMessageSerializer',
    'UserMessageSerializer',
    'SystemMessageSerializer',
    'MessageSerializer',
    'CombinedMessageSerializer',
    'StructuredSuggestedPrompt',
]


class StructuredSuggestedPrompt(CustomModel):
    """Schema for structured suggested prompt."""

    type: str | None = None
    title: str | None = None
    body: str | None = None


class MessageValidator(CustomModel):
    """Schema for creating a new conversation message."""

    conversation_id: UUID
    role: MessageRole = Field(serialization_alias='Role')
    type: MessageType = Field(serialization_alias='Type')
    content: str = Field(serialization_alias='Content')
    options: Sequence[Option] = Field(default=[], serialization_alias='Options')  # NOTE: For user messages only
    selected_option: Option | None = Field(
        default=None, serialization_alias='SelectedOption'
    )  # NOTE: For system messages only
    files: list[UploadFile] | None = None
    intention: ConversationMessageIntention | None = Field(default=None, serialization_alias='Intention')
    suggested_prompts: list[str] = Field(default=[], serialization_alias='SuggestedPrompts')
    system_reply_type: SystemReplyType | None = Field(serialization_alias='SystemReplyType')
    is_error: bool = Field(default=False, serialization_alias='IsError')
    page_type: PageType | None = Field(serialization_alias='PageType', default=None)

    @property
    def as_suggested_reply(self) -> SuggestedUserPrompt | None:
        try:
            return SuggestedUserPrompt(self.content.strip())
        except ValueError:
            return None

    @field_validator('suggested_prompts', mode='before')
    @classmethod
    def validate_suggested_prompts(cls, value: str | list | None) -> list[str]:
        return validate_value_as_list_of_strings(value)

    @field_validator('files')
    @classmethod
    def validate_files(cls, files: list[UploadFile] | None) -> list[UploadFile] | None:
        return validate_files_core(files)

    def model_dump_for_db(self) -> dict:
        result = super().model_dump(by_alias=True)
        for field_name in ('conversation_id', 'files', 'IsError'):
            result.pop(field_name, None)
        for field_name in ('Options', 'SelectedOption'):
            result[field_name] = None if (field_value := result[field_name]) is None else json.dumps(field_value)
        return result


class BaseMessageSerializer(CustomModel):
    id: UUID = Field(validation_alias='PublicId')
    conversation_id: UUID = Field(validation_alias='ConversationPublicId')
    role: MessageRole = Field(validation_alias='Role')
    type: MessageType = Field(validation_alias='Type')
    content: str = Field(validation_alias='Content')
    created_at: datetime = Field(validation_alias='CreatedAt')
    page_type: PageType | None = Field(validation_alias='PageType', default=None)

    model_config = ConfigDict(
        from_attributes=True,
    )

    @property
    def cleaned_content(self) -> str:
        return self.content.strip()


class UserMessageSerializer(BaseMessageSerializer):
    selected_option: Option | None = Field(discriminator='type', validation_alias='SelectedOption')
    translation: str | None = Field(validation_alias='Translation', default=None)

    @field_validator('selected_option', mode='before')
    @classmethod
    def validate_selected_option(cls, value: str | None) -> dict | None:
        if value is None:
            return None

        option = json.loads(value)
        option['type'] = OptionType(option['type'])
        return option

    @property
    def as_suggested_reply(self) -> SuggestedUserPrompt | None:
        try:
            return SuggestedUserPrompt(self.cleaned_content)
        except ValueError:
            return None


class SystemMessageSerializer(BaseMessageSerializer):
    options: Sequence[Annotated[Option, Field(discriminator='type')]] = Field(validation_alias='Options')
    suggested_prompts: list[str] = Field(default=[], validation_alias='SuggestedPrompts')
    system_reply_type: SystemReplyType | None = Field(validation_alias='SystemReplyType')

    @field_validator('options', mode='before')
    @classmethod
    def validate_options(cls, value: str) -> list[dict]:
        options = json.loads(value)
        for option in options:
            option['type'] = OptionType(option['type'])
        return options

    @field_validator('suggested_prompts', mode='before')
    @classmethod
    def validate_suggested_prompts(cls, value: str | list | None) -> list[str]:
        return validate_value_as_list_of_strings(value)


MessageSerializer = UserMessageSerializer | SystemMessageSerializer


class CombinedMessageSerializer(CustomModel):
    """Schema for conversation messages response."""

    user: UserMessageSerializer
    system: SystemMessageSerializer | None = None
    files: list[DocumentResponse] | None = None
    collected_data: dict[str, Any] | None = None


class ConversationMessageIntentClassifierServiceResponse(CustomModel):
    """Schema for conversation message intention classifier response."""

    intention: ConversationMessageIntention


class ConversationMessageProcessingResult(CustomModel):
    """Result of conversation message processing."""

    intention: ConversationMessageIntention
    system_reply: str
    system_reply_type: SystemReplyType
    data: dict[str, Any]
