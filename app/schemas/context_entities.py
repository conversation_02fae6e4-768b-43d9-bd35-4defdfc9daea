from dataclasses import dataclass
from typing import Any, Sequence
from uuid import UUID

from constants.extracted_data import ConversationState
from constants.message import MessageRole, SuggestedUserPrompt, SystemReplyType
from schemas.conversation_message.message import CombinedMessageSerializer

from .confirmed_data import ConfirmedData
from .conversation_message.message import BaseMessageSerializer, MessageValidator
from .conversation_message.option import Option
from .extracted_data import AggregatedData


__all__ = ['ConversationData', 'MessageContent']


@dataclass
class ConversationData:
    """Container for conversation-related data."""

    conversation_message_history: list[CombinedMessageSerializer]
    aggregated_data: AggregatedData
    confirmed_data: ConfirmedData
    conversation: Any  # Database model object with State attribute

    @property
    def should_use_client_confirmation(self) -> bool:
        return bool(
            self.conversation
            and str(self.conversation.State) == str(ConversationState.COLLECTING_CLIENT_NAME)
            and not self.confirmed_data.client_name
            and self.aggregated_data.is_client_name_complete
        )


@dataclass
class MessageContent:
    """Container for system message content and metadata."""

    formatted_content: str
    reply_type: SystemReplyType
    extracted_options: Sequence[Option]
    suggested_prompts: Sequence[SuggestedUserPrompt]

    def to_message_validator(self, conversation_id: UUID, last_message: BaseMessageSerializer) -> MessageValidator:
        """Convert to MessageValidator for message creation."""
        return MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=last_message.type,
            content=self.formatted_content,
            system_reply_type=self.reply_type,
            options=self.extracted_options,
            suggested_prompts=[str(i) for i in self.suggested_prompts],
        )
