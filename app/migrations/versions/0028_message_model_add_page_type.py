"""message model add page_type

Revision ID: 0028
Revises: 0027
Create Date: 2025-08-02 15:57:57.265632

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0028'
down_revision: Union[str, None] = '0027'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'QualConversationMessage',
        sa.Column(
            'PageType',
            sa.Enum('prompt', 'engagement_details', name='pagetype'),
            server_default=sa.text("'prompt'"),
            nullable=False,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualConversationMessage', 'PageType')
    # ### end Alembic commands ###
