import logging
from typing import cast
from uuid import UUID

from config import settings
from constants.engagement import engagement_templates
from constants.message import (
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    PageType,
    SuggestedUserPrompt,
    SystemReplyType,
)
from exceptions import EntityNotFoundError
from repositories import ConversationRepository
from schemas import (
    ConversationCreationRequest,
    ConversationExtraData,
    ConversationResponse,
    ConversationWithWelcomeMessageResponse,
    KXDashTaskOption,
    MessageValidator,
    SystemMessageSerializer,
)
from schemas.confirmed_data import ConfirmedData

from .conversation_message import ConversationMessageService
from .document import DocumentService
from .extracted_data import ExtractedDataService
from .kx_dash import KXDashService


__all__ = ['ConversationService']


logger = logging.getLogger(__name__)


class ConversationService:
    """Service for conversation-related business logic."""

    def __init__(
        self,
        conversation_repository: ConversationRepository,
        conversation_message_service: ConversationMessageService,
        extracted_data_service: ExtractedDataService,
        document_service: DocumentService,
        kx_dash_service: KXDashService,
    ):
        self.conversation_repository = conversation_repository
        self.conversation_message_service = conversation_message_service
        self.extracted_data_service = extracted_data_service
        self.document_service = document_service
        self.kx_dash_service = kx_dash_service

    async def create(
        self, conversation_data: ConversationCreationRequest, user_id: UUID, user_name: str
    ) -> ConversationResponse:
        """
        Create a new conversation.

        Args:
            conversation_data: Data for creating the conversation

        Returns:
            Response with the created conversation data

        Raises:
            DatabaseException: If there's an error creating the conversation
        """
        try:
            logger.debug('Creating a new conversation')
            conversation = await self.conversation_repository.create(
                conversation_data=conversation_data, user_id=user_id, user_name=user_name
            )
            return ConversationResponse.model_validate(conversation, from_attributes=True)
        except Exception as e:  # pragma: no cover
            logger.error('Error creating conversation: %s', e)
            raise

    async def create_with_welcome_message(
        self, conversation_data: ConversationCreationRequest, user_id: UUID, user_name: str, token: str
    ) -> ConversationWithWelcomeMessageResponse:
        """
        Create a new conversation with a welcome message.

        Args:
            conversation_data: Data for creating the conversation

        Returns:
            Response with the created conversation data

        Raises:
            DatabaseException: If there's an error creating the conversation
        """
        try:
            conversation = await self.create(conversation_data, user_id, user_name)

            welcome_message = await self._create_welcome_message(
                conversation.id, token, dash_activity_id=conversation_data.dash_activity_id
            )

            return ConversationWithWelcomeMessageResponse(
                conversation=conversation,
                welcome_message=cast(SystemMessageSerializer, welcome_message),
            )
        except Exception as e:  # pragma: no cover
            logger.error('Error creating conversation with welcome message: %s', e)
            raise

    async def _create_welcome_message(
        self, conversation_id: UUID, token: str, dash_activity_id: int | None = None
    ) -> SystemMessageSerializer:
        """
        Create appropriate welcome message based on available dash tasks.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            SystemMessageSerializer with the welcome message

        Raises:
            Exception: If there's an error creating the welcome message
        """

        if dash_activity_id:
            dash_task = await self.kx_dash_service.get(dash_activity_id, token)
            if not dash_task:
                raise EntityNotFoundError('Dash task', str(dash_activity_id))
            kx_dash_tasks = [dash_task]
        else:
            kx_dash_tasks = await self.kx_dash_service.list(token)

        if kx_dash_tasks:
            options = tuple(
                KXDashTaskOption(
                    activity_id=task.activity_id,
                    client_name=task.client_name,
                    engagement_code=task.engagement_code,
                )
                for task in kx_dash_tasks
            )
            if len(options) == 1:
                message = MessageValidator(
                    conversation_id=conversation_id,
                    role=MessageRole.SYSTEM,
                    type=MessageType.TEXT,
                    content=SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_TASK.message_text,
                    system_reply_type=SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_TASK,
                    options=options,
                    suggested_prompts=[
                        SuggestedUserPrompt.NO_CREATE_NEW_QUAL.value,
                    ],
                )
                return cast(SystemMessageSerializer, await self.conversation_message_service.create_message(message))

            reply_type = SystemReplyType.WELCOME_MESSAGE_WITH_MANY_DASH_TASKS
            suggested_prompts = [
                SuggestedUserPrompt.NO_CREATE_NEW_QUAL.value,
            ]
        else:
            options = ()
            reply_type = SystemReplyType.WELCOME_MESSAGE
            suggested_prompts = [
                SuggestedUserPrompt.UPLOAD_DOCUMENT.value,
                SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION.value,
            ]

        reply_message = reply_type.message_text
        if reply_type == SystemReplyType.WELCOME_MESSAGE:
            reply_message += SystemReplyType.WELCOME_MESSAGE_REMINDER.message_text

        message = await self.conversation_message_service.create_message(
            MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=reply_message,
                system_reply_type=reply_type,
                options=options,
                suggested_prompts=suggested_prompts,
            )
        )
        return cast(SystemMessageSerializer, message)

    async def get(self, public_id: UUID) -> ConversationResponse:
        """
        Get a conversation by its ID.

        Args:
            public_id: The public ID of the conversation

        Returns:
            The conversation response

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the conversation
        """
        try:
            logger.debug('Retrieving conversation with ID: %s', public_id)
            conversation = await self.conversation_repository.get(public_id)

            if not conversation:  # pragma: no cover
                # NOTE: excluded from coverage sinse it's not reachable in test with current permission implementation
                raise EntityNotFoundError('Conversation', str(public_id))

            return ConversationResponse.model_validate(conversation, from_attributes=True)
        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving conversation: %s', e)
            raise

    async def delete(self, conversation_id: UUID) -> None:
        """
        Delete a conversation by ID.

        Args:
            conversation_id: UUID of the conversation to delete

        Raises:
            EntityNotFoundError: If conversation with the given ID does not exist
        """
        try:
            logger.debug('Deleting conversation with ID: %s', conversation_id)
            await self.document_service.delete_many(conversation_id)
            await self.conversation_message_service.delete_many(conversation_id)
            await self.extracted_data_service.delete_many(conversation_id)
            await self.conversation_repository.delete(conversation_id)
            logger.info('Successfully deleted conversation with ID: %s', conversation_id)
        except EntityNotFoundError:  # pragma: no cover
            # NOTE: excluded from coverage sinse it's not reachable in test with current permission implementation
            logger.warning('Conversation with ID %s not found for deletion', conversation_id)
            raise
        except Exception as e:  # pragma: no cover
            logger.error('Failed to delete conversation %s: %s', conversation_id, e, exc_info=True)
            raise

    async def get_owner_id(self, conversation_id: UUID) -> UUID | None:
        """
        Get an owner ID for the conversation.

        Args:
            conversation_id: The public ID of the conversation

        Returns:
            The owner ID of the conversation

        """
        try:
            logger.debug('Retrieving an owner ID for the conversation with ID: %s', conversation_id)
            return await self.conversation_repository.get_owner_id(conversation_id)

        except Exception:  # pragma: no cover
            logger.exception('Failed to retrieve an owner ID for the conversation with ID "%s"', conversation_id)
            raise

    async def get_extra_data(self, public_id: UUID) -> ConversationExtraData:
        """
        Get conversation with extracted data ojects list by conversation public ID.

        Args:
            conversation_id: The public ID of the conversation

        Returns:
            The conversation itself.

        """

        try:
            logger.debug('Retrieving extended data of conversation with ID: %s', public_id)
            conversation = await self.conversation_repository.get_extra_data(public_id)

            if not conversation:  # pragma: no cover
                # NOTE: excluded from coverage sinse it's not reachable in test with current permission implementation
                raise EntityNotFoundError('Conversation', str(public_id))

            extra_data = ConversationExtraData.model_validate(conversation, from_attributes=True)
            extra_data.api_version = settings.version
            return extra_data
        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving extended conversation: %s', e)
            raise

    async def get_confirmed_data(self, conversation_id: UUID) -> ConfirmedData:
        """
        Get the confirmed data for a conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            ConfirmedData object (empty if no data exists)
        """
        conversation_exists = await self.conversation_repository.exists(conversation_id)
        if not conversation_exists:
            raise EntityNotFoundError('Conversation', str(conversation_id))
        confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)

        if not confirmed_data:
            raise EntityNotFoundError('Conversation.ConfirmedData', str(conversation_id))

        if confirmed_data.is_empty:
            raise EntityNotFoundError('Conversation.ConfirmedData', str(conversation_id))

        return confirmed_data

    async def update_qual_id(self, conversation_id: UUID, qual_id: str) -> ConversationResponse:
        """
        Update the QualId for a conversation.

        Args:
            conversation_id: The ID of the conversation
            qual_id: The QualId to set

        Returns:
            The updated conversation response

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        logger.debug('Updating QualId for conversation ID: %s to %s', conversation_id, qual_id)
        await self.conversation_repository.update_qual_id(conversation_id, qual_id)
        return await self.get(conversation_id)

    async def start_engagement_assistant_mode(
        self, conversation_id: UUID, page_type: PageType
    ) -> SystemMessageSerializer:
        """
        Start a new assistant mode for a conversation.

        Args:
            conversation_id: The ID of the conversation
            page_type: The PageType to set for the new assistant mode

        Returns:
            The updated conversation response

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        logger.debug('Starting assistant mode for conversation ID: %s with page type: %s', conversation_id, page_type)
        msg = await self._create_welcome_engagement_message(conversation_id, page_type)
        return msg

    async def _create_welcome_engagement_message(
        self, conversation_id: UUID, page_type: PageType = PageType.ENGAGEMENT_DETAILS
    ) -> SystemMessageSerializer:
        """
        Create appropriate welcome message based on available dash tasks.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            SystemMessageSerializer with the welcome message

        Raises:
            Exception: If there's an error creating the welcome message
        """
        await self.conversation_message_service.create_message(
            MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.USER,
                type=MessageType.TEXT,
                content='',
                intention=ConversationMessageIntention.UNDEFINED,
                system_reply_type=SystemReplyType.EMPTY,
            )
        )
        message = await self.conversation_message_service.create_message(
            MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=engagement_templates.welcome.SYSTEM,
                system_reply_type=SystemReplyType.WELCOME_MESSAGE,
                page_type=page_type,
            )
        )
        return cast(SystemMessageSerializer, message)
