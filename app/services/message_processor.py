from dataclasses import dataclass
from functools import cached_property
import logging
from typing import Any, Optional
from uuid import UUID

from fastapi import UploadFile

from constants.extracted_data import (
    ConfirmedDataFields,
    ConversationState,
    DataSourceType,
    MissingDataStatus,
    RequiredField,
)
from constants.message import ConversationMessageIntention, SuggestedUserPrompt, SystemReplyType
from exceptions import AIBadRequestError, EntityNotFoundError
from repositories import ConversationMessageRepository, ConversationRepository
from schemas import (
    ClientCreateRequest,
    ClientSearchRequest,
    ConversationData,
    ConversationMessageIntentClassifierServiceResponse,
    ConversationMessageProcessingResult,
    DatePickerOption,
    QualQueueMessage,
)
from schemas.confirmed_data import ConfirmedData
from schemas.conversation_message.message import CombinedMessageSerializer, UserMessageSerializer
from schemas.extracted_data import AggregatedData
from schemas.queue_message import Source

from .date_validator import DateValidatorService
from .document import DocumentService
from .extracted_data import ExtractedDataService
from .intent_classifier import IntentClassifierService
from .mixins import CombinedHistoryMixin


MAX_CLIENT_NAME_LENGTH = 50


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class ExtractionProcessingResult:
    """Result of uncertainty intention processing."""

    system_reply: str
    system_reply_type: SystemReplyType
    missing_data_status: MissingDataStatus
    next_expected_field: str | None = None
    missing_fields: list[str] | None = None
    options: list[str] | list[tuple[str | None, str | None]] | None = None
    conversation_state: Optional[ConversationState] = None


@dataclass(frozen=True)
class ConversationMessageProcessor(CombinedHistoryMixin):
    """Processor for conversation message intention."""

    conversation_id: UUID
    user_message: UserMessageSerializer
    intent_classifier_service: IntentClassifierService
    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository
    conversation_message_repository: ConversationMessageRepository
    date_validator_service: DateValidatorService
    document_service: DocumentService
    conversation_message_history: list[CombinedMessageSerializer]
    conversation_data: ConversationData | None = None
    files: list[UploadFile] | None = None
    token: str = ''

    @cached_property
    def _cleaned_user_message(self) -> str:
        return self.user_message.content.strip()

    async def _get_intent(self) -> ConversationMessageIntention:
        """Intent getter."""

        ########################
        # Pre-processing before intent classification

        # Process empty user message
        if not self._cleaned_user_message:
            if self.files:
                return ConversationMessageIntention.EXTRACTION
            else:
                return ConversationMessageIntention.UNDEFINED

        # Process user message as suggested reply
        user_message_as_suggested_reply: SuggestedUserPrompt | None = self.user_message.as_suggested_reply
        latest_system_message_type: SystemReplyType | None = self._latest_system_message_type

        if user_message_as_suggested_reply:
            if user_message_as_suggested_reply == SuggestedUserPrompt.NO_CREATE_NEW_QUAL:  # Case 1, 2
                return ConversationMessageIntention.DASH_DISCARD
            elif user_message_as_suggested_reply == SuggestedUserPrompt.SHOW_ME_AN_EXAMPLE_PROMPT:  # Case 3, 4, 5
                return ConversationMessageIntention.EXAMPLE
            elif user_message_as_suggested_reply in [
                SuggestedUserPrompt.NO_CREATE_MY_QUAL,
                SuggestedUserPrompt.CONTINUE_WITHOUT_ERROR_FILE,
            ]:  # Case 6, 7
                return ConversationMessageIntention.GENERATE_QUAL
            elif user_message_as_suggested_reply == SuggestedUserPrompt.ENTER_A_NEW_CLIENT:  # Case 8, 9
                return ConversationMessageIntention.PROVIDE_CLIENT_NAME
            elif user_message_as_suggested_reply == SuggestedUserPrompt.YES_THIS_IS_CORRECT:  # Case 10, 11
                return ConversationMessageIntention.USER_CONFIRMATION
            elif user_message_as_suggested_reply == SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME:  # Case 12, 13
                return ConversationMessageIntention.PROVIDE_CLIENT_NAME
            elif user_message_as_suggested_reply == SuggestedUserPrompt.YES:  # Case 14, 15
                return ConversationMessageIntention.USER_CONFIRMATION

            # Welcome message suggested prompts
            elif user_message_as_suggested_reply == SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION:
                raise ValueError(
                    'This reply should be processed before _get_intent is called. Please validate the logic'
                )
            elif user_message_as_suggested_reply == SuggestedUserPrompt.UPLOAD_DOCUMENT:
                raise ValueError(
                    'This reply should be processed at the frontend before sending files. Please validate the logic'
                )
            elif user_message_as_suggested_reply == SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM:
                return ConversationMessageIntention.MANUAL_LDMF_INPUT
            else:
                raise NotImplementedError(f'Suggested user prompt {user_message_as_suggested_reply} not implemented')

        elif latest_system_message_type:
            if latest_system_message_type in SystemReplyType.get_data_complete_replies():
                # LLM sometimes interprets this intent incorrectly as USER_CONFIRMATION instead of GENERATE_QUAL, especially when single `no` is used.
                user_message_is_manual_qual_creation_request = self.user_message.cleaned_content.lower() in [
                    'no',
                    'create qual',
                    'create my qual',
                    'create a qual',
                    'create a new qual',
                    'no create a new qual',
                ]
                if user_message_is_manual_qual_creation_request:
                    return ConversationMessageIntention.GENERATE_QUAL
            elif latest_system_message_type == SystemReplyType.NEED_INFO_LDMF_COUNTRY:
                # When the system asks for LDMF country, treat any user input as manual LDMF input
                return ConversationMessageIntention.MANUAL_LDMF_INPUT
            elif latest_system_message_type in SystemReplyType.get_data_input_request_replies():
                # If previous system reply starts with NEED_INFO*, then we can manually interpret it as "EXTRACTION"
                # Without this manual check LLM sometimes interprets user description of outcomes or objectives as "UNDEFINED"
                return ConversationMessageIntention.EXTRACTION

        ########################
        # Intent classification
        try:
            intent_classification_response: ConversationMessageIntentClassifierServiceResponse = (
                await self.intent_classifier_service.classify_intent(
                    user_message=self._cleaned_user_message,
                    response_cls=ConversationMessageIntentClassifierServiceResponse,
                )
            )
            intention = intent_classification_response.intention
        except AIBadRequestError:
            intention = ConversationMessageIntention.UNDEFINED

        logger.info(f'Classified intention: {intention}')
        return intention

    async def run(self) -> ConversationMessageProcessingResult:
        """Process the intention."""
        aggregated_data = await self.extracted_data_service.aggregate_data(self.conversation_id)

        ########################
        # Pre-processing before intent handler run

        user_message_as_suggested_reply: SuggestedUserPrompt | None = self.user_message.as_suggested_reply
        if user_message_as_suggested_reply:
            if user_message_as_suggested_reply == SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION:
                reply_type = SystemReplyType.BRIEF_DESCRIPTION
                return ConversationMessageProcessingResult(
                    intention=ConversationMessageIntention.UNCERTAINTY,
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    data={},
                )

        user_enters_new_client = self._latest_system_message_type in [
            SystemReplyType.CLIENT_NAME_TOO_LONG,
            SystemReplyType.PROVIDE_CLIENT_NAME_UNSURE,
            SystemReplyType.NEED_INFO_CLIENT_NAME,
        ]
        logger.info(f'User enters a new client: {user_enters_new_client}')

        user_confirms_single_client = (
            aggregated_data.is_client_name_complete and aggregated_data.client_name[0] == self._cleaned_user_message
        )
        logger.info(f'User confirms single client: {user_confirms_single_client}')

        user_confirms_one_from_many = (
            aggregated_data.client_name and len(aggregated_data.client_name) > 1
        ) and self._cleaned_user_message in aggregated_data.client_name
        logger.info(f'User confrims one or many: {user_confirms_single_client}')

        if user_confirms_single_client or user_confirms_one_from_many or user_enters_new_client:
            data = await self._handle_client_name_input(
                self._cleaned_user_message, called_from=ConversationMessageProcessor.run.__name__
            )
            return ConversationMessageProcessingResult(
                intention=ConversationMessageIntention.USER_CONFIRMATION,
                system_reply=data.system_reply,
                system_reply_type=data.system_reply_type,
                data=self.dataclass_to_dict(data),
            )

        intention = await self._get_intent()
        logger.info(f'User intention is {intention}')

        if intention == ConversationMessageIntention.UNDEFINED:
            data = self._process_undefined()
        elif intention == ConversationMessageIntention.GENERATE_QUAL:
            conversation = await self.conversation_repository.get(self.conversation_id)
            if conversation is not None and str(conversation.State) == ConversationState.INITIAL:
                data = await self._dash_discard()
            else:
                data = await self._generate_qual(aggregated_data)
        elif intention == ConversationMessageIntention.EXTRACTION:
            data = await self._extract_data()
        elif intention == ConversationMessageIntention.EXAMPLE:
            data = self._example_help()
        elif intention == ConversationMessageIntention.DASH_DISCARD:
            data = await self._dash_discard()
        elif intention == ConversationMessageIntention.UNCERTAINTY:
            data = self._uncertainty(aggregated_data)
        elif intention == ConversationMessageIntention.NEED_CONTEXT:
            data = self._uncertainty(aggregated_data)
        elif intention == ConversationMessageIntention.USER_CONFIRMATION:
            data = await self._user_confirmation()
        elif intention == ConversationMessageIntention.CHANGE_ENGAGEMENT_DATES:
            conversation = await self.conversation_repository.get(self.conversation_id)
            if str(conversation.State) in (
                ConversationState.INITIAL,
                ConversationState.COLLECTING_CLIENT_NAME,
                ConversationState.COLLECTING_COUNTRY,
            ):
                data = self._uncertainty(aggregated_data)
            else:
                data = await self._change_engagement_dates()
        elif intention == ConversationMessageIntention.PROVIDE_CLIENT_NAME:
            data = await self._provide_client_name()
        elif intention == ConversationMessageIntention.MANUAL_LDMF_INPUT:
            data = await self._manual_ldmf_input()
        elif intention == ConversationMessageIntention.USER_DENIAL:
            data = await self._user_denial(aggregated_data)
        else:
            raise NotImplementedError(f'Intent {intention} not implemented')

        ########################
        # Post-processing after intent handler run
        # If the data is an instance of UncertaintyProcessingResult, convert it to a dict
        # for compatibility with ConversationMessageProcessingResult

        system_reply = data.system_reply
        system_reply_type = data.system_reply_type
        conversation_state = data.conversation_state
        if conversation_state:
            await self.conversation_repository.update_state(self.conversation_id, conversation_state)

        # Convert dataclass to dict, excluding None values
        data_dict = self.dataclass_to_dict(data)

        return ConversationMessageProcessingResult(
            intention=intention,
            system_reply=system_reply,
            system_reply_type=system_reply_type,
            data=data_dict,
        )

    def dataclass_to_dict(self, data: ExtractionProcessingResult) -> dict[str, Any]:
        data_dict = {
            k: v for k, v in data.__dict__.items() if v is not None and k not in ('system_reply', 'system_reply_type')
        }
        return data_dict

    ########################
    # intention handlers

    def _process_undefined(self) -> ExtractionProcessingResult:
        reply_type = SystemReplyType.UNDEFINED
        return ExtractionProcessingResult(
            system_reply=reply_type.message_text,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
        )

    async def _generate_qual(self, aggregated_data: AggregatedData) -> ExtractionProcessingResult:
        try:
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(self.conversation_id))

            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=self.conversation_id,
                token=self.token,
                confirmed_data=confirmed_data,
                called_from='ConversationMessageProcessor._generate_qual',
                conversation_data=self.conversation_data,
            )
            if (
                missing_data_response.missing_fields
                in [
                    [str(RequiredField.LDMF_COUNTRY)],
                    [str(RequiredField.ENGAGEMENT_DATES)],
                    [str(RequiredField.OBJECTIVE_SCOPE)],
                    [str(RequiredField.OUTCOMES)],
                ]
                and confirmed_data.client_name is None
            ):
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=self.conversation_id,
                    field_name=str(ConfirmedDataFields.CLIENT_NAME),
                    field_value=aggregated_data.client_name[0],
                    state=ConversationState.COLLECTING_COUNTRY,
                )

            state = confirmed_data.get_current_conversation_state()
            # ask user for missing data
            if missing_data_response.status == MissingDataStatus.MISSING_DATA:
                await self.conversation_repository.update_state(self.conversation_id, state)
                return ExtractionProcessingResult(
                    system_reply=missing_data_response.message or '',
                    system_reply_type=missing_data_response.reply_type or SystemReplyType.MISSING_REQUIRED_DATA,
                    missing_data_status=missing_data_response.status,
                    next_expected_field=missing_data_response.next_expected_field,
                    missing_fields=missing_data_response.missing_fields,
                    options=missing_data_response.options,
                    conversation_state=missing_data_response.conversation_state,
                )

            # user provided all the data, generate a qual
            else:
                # Collect and send relevant messages for comprehensive extraction
                await self._send_comprehensive_extraction_queue_message()

                await self.conversation_repository.update_state(self.conversation_id, ConversationState.QUAL_CREATED)
                reply_type = SystemReplyType.READY_TO_GENERATE_QUAL_REPLY
                confirmation_message = reply_type.message_text.format(client_name=confirmed_data.client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.DATA_COMPLETE,
                )
        except Exception as e:
            logger.error('Exception in _generate_qual method for conversation %s: %s', self.conversation_id, e)
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )

    async def _extract_data(self) -> ExtractionProcessingResult:
        # Note: Text prompt creation is now handled in the unified queue approach
        # in ConversationMessageService.create() method

        user_message = self._cleaned_user_message

        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(self.conversation_id))

            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
            aggregated_data = await self.extracted_data_service.aggregate_data(self.conversation_id)

            client_name_was_confirmed = await self._handle_manual_input(
                conversation_id=self.conversation_id,
                conversation_state=ConversationState(conversation.State),
                aggregated_data=aggregated_data,
                confirmed_data=confirmed_data,
                message=user_message,
            )
            if client_name_was_confirmed:
                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text.format(client_name=user_message),
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                )

            state_is_initial = str(conversation.State) == str(ConversationState.INITIAL)
            if state_is_initial and user_message:
                reply_type = SystemReplyType.EMPTY
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                )

            # Check if user is providing manual input for client name
            if (
                str(conversation.State) == ConversationState.COLLECTING_CLIENT_NAME.value
                and confirmed_data.client_name is None
                and user_message
            ):
                # Check if user is responding to client creation prompt
                if confirmed_data.proposed_client_name is not None:
                    return await self._handle_client_creation_response(
                        proposed_client_name=confirmed_data.proposed_client_name
                    )

                # Extract client name from user message and process it
                client_name = user_message
                return await self._handle_client_name_input(
                    client_name, called_from=ConversationMessageProcessor._extract_data.__name__
                )

            # Check if user is providing manual input for dates
            elif str(conversation.State) == ConversationState.COLLECTING_DATES.value and user_message:
                # Extract dates from user message and process it
                return await self._handle_dates_input(user_message)

            # Check if user is providing manual input for country
            elif (
                str(conversation.State) == ConversationState.COLLECTING_COUNTRY.value
                and confirmed_data.ldmf_country is None
                and user_message
            ):
                ldmf_country = user_message
                return await self._handle_country_input(ldmf_country)

            elif (
                str(conversation.State) == ConversationState.COLLECTING_OBJECTIVE.value
                and confirmed_data.objective_and_scope is None
                and user_message
            ):
                return await self._handle_objective_and_scope_input(user_message)

            elif (
                str(conversation.State) == ConversationState.COLLECTING_OUTCOMES.value
                and confirmed_data.outcomes is None
                and user_message
            ):
                return await self._handle_outcomes_input(user_message)

            # Handle user response when in DATA_COMPLETE state (after showing CONFIRMED_FIELDS_READY)
            elif str(conversation.State) == ConversationState.DATA_COMPLETE.value and user_message:
                return await self._handle_data_complete_response(user_message, confirmed_data)

            # Check what data is missing using the enhanced service
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=self.conversation_id,
                token=self.token,
                confirmed_data=confirmed_data,
                called_from='ConversationMessageProcessor._extract_data',
                conversation_data=self.conversation_data,
            )

            # Update conversation state based on the response
            if missing_data_response.status == MissingDataStatus.MISSING_DATA:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )
                return ExtractionProcessingResult(
                    system_reply=missing_data_response.message or '',
                    system_reply_type=SystemReplyType.MISSING_REQUIRED_DATA,
                    missing_data_status=missing_data_response.status,
                    next_expected_field=missing_data_response.next_expected_field,
                    missing_fields=missing_data_response.missing_fields,
                    options=missing_data_response.options,
                    conversation_state=missing_data_response.conversation_state,
                )

            elif missing_data_response.status == MissingDataStatus.DATA_COMPLETE:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )
                reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=missing_data_response.status,
                )

            else:  # error status
                logger.error(
                    'Error in missing data collection for conversation %s: %s',
                    self.conversation_id,
                    missing_data_response.message,
                )
                reply_type = SystemReplyType.BRIEF_DESCRIPTION
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.ERROR,
                )

        except Exception as e:
            logger.error('Exception in _extract_data method for conversation %s: %s', self.conversation_id, e)
            # Fallback to original behavior on any error
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )

    async def _dash_discard(self) -> ExtractionProcessingResult:
        await self.extracted_data_service.delete(self.conversation_id, DataSourceType.KX_DASH)
        reply_type = SystemReplyType.WELCOME_MESSAGE
        return ExtractionProcessingResult(
            system_reply=reply_type.message_text,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
        )

    def _example_help(self) -> ExtractionProcessingResult:
        reply_type = SystemReplyType.EXAMPLE
        return ExtractionProcessingResult(
            system_reply=reply_type.message_text,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
        )

    def _uncertainty(self, aggregated_data: AggregatedData) -> ExtractionProcessingResult:
        if aggregated_data.is_empty:
            """
            USE CASE 11 - NEED INFO:
            GIVEN the user navigates to the Prompt page
            AND the user doesn't provide a prompt containing the data for qual creation (bare minimum 5 fields)
            WHEN the user enters the text in input to provide some instruction (for example "Not sure what to do")
            VERIFY that the user can see the following message: "I'll need some more details in order to create a draft
            qual. Could you start by telling me about the client and what services we delivered?"
            AND the AI will ask sequential questions to collect all 5 fields one by one: (User Story 2195552)
            """
            reply_type = SystemReplyType.UNCERTAINTY_EMPTY_AGGREGATION
        else:
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
        return ExtractionProcessingResult(
            system_reply=reply_type.message_text,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
        )

    ########################
    # utils for extraction & confirmation handlers

    async def _analyze_dates_message(self, message: str) -> tuple[DatePickerOption, bool]:
        """
        Process message content and determine system reply.

        Args:
            message: The user message to analyze

        Returns:
            Tuple with date option and sure flag
        """
        date_validator_response = await self.date_validator_service.find_dates(user_message=message)

        start_date = date_validator_response.date_1
        end_date = date_validator_response.date_2

        sure = True
        for date in (start_date, end_date):
            if (
                date
                and date.day <= 12
                and not (await self.date_validator_service.date_is_text(user_message=message, date=date.isoformat()))
            ):
                sure = False
                break

        # Sort dates if both are present
        if start_date and end_date and end_date < start_date:
            start_date, end_date = end_date, start_date

        # If multiple dates are detected, we need to show a date picker
        if date_validator_response.more_than_two_dates:
            sure = False

        return DatePickerOption(start_date=start_date, end_date=end_date), sure

    async def _handle_dates_input(self, user_message: str) -> ExtractionProcessingResult:
        """
        Handle dates input by analyzing the message and determining next steps.

        Args:
            message: The user message to analyze

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """
        date_option, sure = await self._analyze_dates_message(user_message)
        start_date = date_option.start_date.isoformat() if date_option.start_date else None
        end_date = date_option.end_date.isoformat() if date_option.end_date else None

        if start_date and end_date and sure:
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=self.conversation_id,
                field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                field_value=(start_date, end_date),
                state=ConversationState.COLLECTING_OBJECTIVE,
            )
            reply_type = SystemReplyType.DATES_CONFIRMED
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.DATA_COMPLETE,
            )
        else:
            await self.conversation_repository.update_state(self.conversation_id, ConversationState.COLLECTING_DATES)
            reply_type = SystemReplyType.DATES_AMBIGUOUS
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.COLLECTING_DATES,
                options=[(start_date, end_date)],
            )

    async def _handle_client_name_input(self, client_name: str, called_from: str = '') -> ExtractionProcessingResult:
        """
        Handle client name input by searching the API and determining next steps.

        Args:
            client_name: The client name to process

        Returns:
            ExtractionProcessingResult with system reply and other data
        """

        try:
            if len(client_name) > MAX_CLIENT_NAME_LENGTH:
                reply_type = SystemReplyType.CLIENT_NAME_TOO_LONG
                confirmation_message = reply_type.message_text.format(client_name=client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    options=[],
                )
            # Search for the client name using the quals client repository
            search_request = ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
            search_result = await self.extracted_data_service.quals_clients_repository.search_clients(
                search_request, self.token
            )

            if search_result.clients and len(search_result.clients) == 1 and not search_result.exact_match:
                # Exactly one match found - auto-confirm
                confirmed_client_name = client_name if search_result.exact_match else search_result.clients[0].name
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=self.conversation_id,
                    field_name=str(ConfirmedDataFields.PROPOSED_CLIENT_NAME),
                    field_value=confirmed_client_name,
                    state=ConversationState.COLLECTING_COUNTRY,
                )

                reply_type = SystemReplyType.CLIENT_NAME_SINGLE_FOUND_CONFIRMATION
                confirmation_message = reply_type.message_text.format(client_name=confirmed_client_name)
                logger.info(
                    '%s system_reply_type detected in %s. Called from %s',
                    reply_type,
                    'ConversationMessageProcessor._handle_client_name_input:one_match_found',
                    called_from,
                )
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                )
            elif search_result.exact_match:
                # Exactly one match found - auto-confirm
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=self.conversation_id,
                    field_name=str(ConfirmedDataFields.CLIENT_NAME),
                    field_value=client_name,
                    state=ConversationState.COLLECTING_COUNTRY,
                )

                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                confirmation_message = reply_type.message_text.format(client_name=client_name)
                logger.info(
                    '%s system_reply_type detected in %s. Called from %s',
                    reply_type,
                    'ConversationMessageProcessor._handle_client_name_input:one_match_found',
                    called_from,
                )
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                )

            elif search_result.clients and len(search_result.clients) > 1:
                # as for 2370678 - should not ask confirmation, just save it. DISREGARD
                # Multiple matches found - ask for confirmation with the original name

                reply_type = SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS
                confirmation_message = reply_type.message_text.format(client_name=client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    options=[client.name for client in search_result.clients],
                )

            else:
                # No matches found - ask if user wants to add as new client
                await self.conversation_repository.update_state(
                    self.conversation_id, ConversationState.COLLECTING_CLIENT_NAME
                )

                # Store the proposed client name in confirmed_data for session persistence
                confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
                confirmed_data.proposed_client_name = client_name
                await self.conversation_repository.update_confirmed_data_and_state(
                    public_id=self.conversation_id,
                    confirmed_data=confirmed_data,
                    state=ConversationState.COLLECTING_CLIENT_NAME,
                )
                reply_type = SystemReplyType.CLIENT_NOT_FOUND
                confirmation_message = reply_type.message_text.format(client_name=client_name)
                logger.info(
                    '%s system_reply_type detected in %s. Called from %s',
                    reply_type,
                    'ConversationMessageProcessor._handle_client_name_input:not_found',
                    called_from,
                )
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    options=[],
                )

        except Exception as e:
            logger.error('Error handling client name input for conversation %s: %s', self.conversation_id, e)
            # Fallback to simple confirmation
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=self.conversation_id,
                field_name=str(ConfirmedDataFields.CLIENT_NAME),
                field_value=client_name,
                state=ConversationState.COLLECTING_COUNTRY,
            )

            reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
            confirmation_message = reply_type.message_text.format(client_name=client_name)
            return ExtractionProcessingResult(
                system_reply=confirmation_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
            )

    async def _handle_client_creation_response(self, proposed_client_name: str) -> ExtractionProcessingResult:
        """
        Handle user response to client creation prompt.

        Args:
            proposed_client_name: The client name that was proposed for creation

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """
        user_message_str = self.user_message.content
        user_response = user_message_str.strip().lower()

        # Check if user confirms adding the new client

        if any(
            keyword in user_response for keyword in [SuggestedUserPrompt.YES.value.lower(), 'add', 'create', 'confirm']
        ):
            try:
                search_request = ClientSearchRequest(contains=proposed_client_name, page_size=5, page_idx=0)
                search_result = await self.extracted_data_service.quals_clients_repository.search_clients(
                    search_request, self.token
                )
                if search_result.exact_match:
                    # Client already exists, update confirmed data and clear proposed name
                    await self.extracted_data_service.update_confirmed_data(
                        conversation_id=self.conversation_id,
                        field_name=str(ConfirmedDataFields.CLIENT_NAME),
                        field_value=proposed_client_name,
                        state=ConversationState.COLLECTING_COUNTRY,
                    )

                    # Clear the proposed client name
                    confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
                    confirmed_data.proposed_client_name = None
                    await self.conversation_repository.update_confirmed_data_and_state(
                        public_id=self.conversation_id,
                        confirmed_data=confirmed_data,
                        state=ConversationState.COLLECTING_COUNTRY,
                    )

                    reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                    confirmation_message = reply_type.message_text.format(client_name=proposed_client_name)
                    return ExtractionProcessingResult(
                        system_reply=confirmation_message,
                        system_reply_type=reply_type,
                        missing_data_status=MissingDataStatus.MISSING_DATA,
                    )

                # Create the new client using the repository
                create_request = ClientCreateRequest(name=proposed_client_name)
                create_result = await self.extracted_data_service.quals_clients_repository.create_client(
                    create_request, self.token
                )

                if create_result.success:
                    # Update confirmed data with the new client name and clear proposed name
                    await self.extracted_data_service.update_confirmed_data(
                        conversation_id=self.conversation_id,
                        field_name=ConfirmedDataFields.CLIENT_NAME.value,
                        field_value=proposed_client_name,
                        state=ConversationState.COLLECTING_COUNTRY,
                    )

                    # Clear the proposed client name
                    confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
                    confirmed_data.proposed_client_name = None

                    state = (
                        ConversationState.DATA_COMPLETE
                        if confirmed_data.required_fields_are_complete
                        else ConversationState.COLLECTING_CLIENT_NAME
                    )
                    await self.conversation_repository.update_confirmed_data_and_state(
                        public_id=self.conversation_id,
                        confirmed_data=confirmed_data,
                        state=state,
                    )

                    reply_type = SystemReplyType.CLIENT_CREATION_CONFIRMED
                    confirmation_message = reply_type.message_text.format(client_name=proposed_client_name)
                    return ExtractionProcessingResult(
                        system_reply=confirmation_message,
                        system_reply_type=reply_type,
                        missing_data_status=MissingDataStatus.MISSING_DATA,
                    )
                else:
                    # Client creation failed
                    reply_type = SystemReplyType.CLIENT_CREATION_UNSUCCESSFUL
                    return ExtractionProcessingResult(
                        system_reply=reply_type.message_text.format(proposed_client_name=proposed_client_name),
                        system_reply_type=reply_type,
                        missing_data_status=MissingDataStatus.MISSING_DATA,
                        conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    )

            except Exception as e:
                logger.error(
                    'Error creating client %s for conversation %s: %s', proposed_client_name, self.conversation_id, e
                )
                reply_type = SystemReplyType.CLIENT_CREATION_FAILED
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text.format(proposed_client_name=proposed_client_name),
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                )

        # If the response contains a potential client name, treat it as a new client name
        else:
            reply_type = SystemReplyType.CLIENT_CREATION_UNSURE
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
            )

    async def _handle_objective_and_scope_input(self, objective_and_scope: str) -> ExtractionProcessingResult:
        """
        Handle user input for objective and scope.

        Args:
            objective_and_scope: The user's input for objective and scope

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """
        # Update confirmed data with the new objective and scope
        await self.extracted_data_service.update_confirmed_data(
            conversation_id=self.conversation_id,
            field_name=ConfirmedDataFields.OBJECTIVE_AND_SCOPE.value,
            field_value=objective_and_scope,
            state=ConversationState.COLLECTING_OUTCOMES,
        )

        reply_type = SystemReplyType.OBJECTIVE_AND_SCOPE_CONFIRMED
        confirmation_message = reply_type.message_text.format(objective_and_scope=objective_and_scope)
        return ExtractionProcessingResult(
            system_reply=confirmation_message,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
        )

    async def _handle_outcomes_input(self, outcomes: str) -> ExtractionProcessingResult:
        """
        Handle user input for outcomes.

        Args:
            outcomes: The user's input for outcomes

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """
        # Update confirmed data with the new outcomes
        await self.extracted_data_service.update_confirmed_data(
            conversation_id=self.conversation_id,
            field_name=ConfirmedDataFields.OUTCOMES.value,
            field_value=outcomes,
            state=ConversationState.DATA_COMPLETE,
        )

        reply_type = SystemReplyType.OUTCOMES_CONFIRMED
        confirmation_message = reply_type.message_text.format(outcomes=outcomes)
        return ExtractionProcessingResult(
            system_reply=confirmation_message,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.DATA_COMPLETE,
        )

    async def _handle_country_input(self, ldmf_country: str) -> ExtractionProcessingResult:
        """
        Handle country input by searching the API and determining next steps.
        """
        try:
            if ldmf_country == SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM:
                reply_type = SystemReplyType.LDMF_COUNTRY_SINGLE_CONFIRMATION
                confirmation_message = reply_type.message_text.format(ldmf_country=ldmf_country)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                    options=[ldmf_country],
                )

            verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                ldmf_country, self.token
            )

            if verified_countries and len(verified_countries) == 1:
                verified_country = verified_countries[0]
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=self.conversation_id,
                    field_name=ConfirmedDataFields.LDMF_COUNTRY.value,
                    field_value=verified_country,
                    state=ConversationState.COLLECTING_DATES,
                )
                reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
                confirmation_message = reply_type.message_text.format(ldmf_country=verified_country)
                logger.info(
                    '%s system_reply_type detected in %s',
                    reply_type,
                    'ConversationMessageProcessor._handle_country_input:success',
                )
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                )

            elif verified_countries and len(verified_countries) > 1:
                # Multiple matches found - ask for confirmation with the original name
                await self.conversation_repository.update_state(
                    self.conversation_id, ConversationState.COLLECTING_COUNTRY
                )

                reply_type = SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS
                confirmation_message = reply_type.message_text
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                    options=verified_countries,
                )

            else:
                # No matches found - ask for confirmation of the ldmf country
                await self.conversation_repository.update_state(
                    self.conversation_id, ConversationState.COLLECTING_COUNTRY
                )
                reply_type = SystemReplyType.LDMF_COUNTRY_SINGLE_CONFIRMATION
                confirmation_message = reply_type.message_text.format(ldmf_country=ldmf_country)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                    options=[ldmf_country],
                )
        except Exception as e:
            logger.error('Error handling country input for conversation %s: %s', self.conversation_id, e)
            # Fallback to simple confirmation
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=self.conversation_id,
                field_name=ConfirmedDataFields.LDMF_COUNTRY.value,
                field_value=ldmf_country,
                state=ConversationState.COLLECTING_COUNTRY,
            )
            reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
            confirmation_message = reply_type.message_text.format(ldmf_country=ldmf_country)
            logger.info(
                '%s system_reply_type detected in %s',
                reply_type,
                'ConversationMessageProcessor._handle_country_input:error',
            )
            return ExtractionProcessingResult(
                system_reply=confirmation_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
            )

    async def _handle_data_complete_response(
        self, user_message: str, confirmed_data: ConfirmedData
    ) -> ExtractionProcessingResult:
        """
        Handle user response when all data is complete and user was asked if they want to add anything else.

        Args:
            user_message: The user's response message
            confirmed_data: The confirmed data containing client_name for formatting

        Returns:
            ExtractionProcessingResult with appropriate system reply
        """
        user_response = user_message.strip().lower()

        # Check if user wants to proceed with qual creation
        if any(
            keyword in user_response
            for keyword in [
                SuggestedUserPrompt.NO_CREATE_MY_QUAL.value.lower(),
                'no',
                'create',
                'generate',
                'proceed',
                'ready',
            ]
        ):
            # Update conversation state to ready for qual creation
            await self.conversation_repository.update_state(
                self.conversation_id, ConversationState.READY_FOR_QUAL_CREATION
            )

            # Format the message with client name from confirmed data
            client_name = confirmed_data.client_name or 'the client'
            reply_type = SystemReplyType.READY_TO_CREATE_DRAFT_QUAL
            formatted_message = reply_type.message_text.format(client_name=client_name)
            return ExtractionProcessingResult(
                system_reply=formatted_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.DATA_COMPLETE,
            )

        # If user wants to add more information, keep them in DATA_COMPLETE state
        # and ask what they'd like to add
        else:
            reply_type = SystemReplyType.ADDITIONAL_DATA_PROPOSAL
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.DATA_COMPLETE,
            )

    ############
    # handlers
    async def _user_confirmation(self) -> ExtractionProcessingResult:
        user_message = self._cleaned_user_message
        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(self.conversation_id))

            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
            aggregated = await self.extracted_data_service.aggregate_data(self.conversation_id)

            client_name_was_confirmed = await self._handle_manual_input(
                conversation_id=self.conversation_id,
                conversation_state=ConversationState(conversation.State),
                aggregated_data=aggregated,
                confirmed_data=confirmed_data,
                message=user_message,
            )
            if client_name_was_confirmed:
                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                client_name = user_message
                if user_message.capitalize() in SuggestedUserPrompt.agreement_prompts():
                    client_name = confirmed_data.proposed_client_name or aggregated.client_name[0]

                system_reply = reply_type.message_text.format(client_name=client_name)
                missing_data_status = MissingDataStatus.MISSING_DATA
                conversation_state = ConversationState.COLLECTING_COUNTRY

                if str(conversation.State) == str(ConversationState.DATA_COMPLETE):
                    reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                    system_reply = ' '.join((system_reply, reply_type.message_text))
                    missing_data_status = MissingDataStatus.DATA_COMPLETE
                    conversation_state = ConversationState.DATA_COMPLETE

                return ExtractionProcessingResult(
                    system_reply=system_reply,
                    system_reply_type=reply_type,
                    missing_data_status=missing_data_status,
                    conversation_state=conversation_state,
                )
            # Check if user is providing manual input for client name
            if (
                str(conversation.State) == ConversationState.COLLECTING_CLIENT_NAME.value
                and confirmed_data.client_name is None
                and aggregated.client_name
            ):
                # Check if user is responding to client creation prompt
                if confirmed_data.proposed_client_name is not None:
                    return await self._handle_client_creation_response(
                        proposed_client_name=confirmed_data.proposed_client_name
                    )

                client_name = aggregated.client_name[0]

                user_message_is_yes = user_message.strip().lower() == SuggestedUserPrompt.YES.value.lower()
                previous_message_is_client_not_found = (
                    self._latest_system_message_type == SystemReplyType.CLIENT_NOT_FOUND
                )
                if user_message_is_yes and previous_message_is_client_not_found:
                    # this is a unique scenario, that happens because for some reason confirmed_data.proposed_client_name is None,
                    # even though client_name was extracted from initial user prompt, and was detected as CLIENT_NOT_FOUND.
                    # So we can safely make this manual check of previous system reply and process current user's message "YES".
                    # TODO: should confirmed_data.proposed_client_name be updated here? or in some other place at the moment of data extraction?
                    return await self._handle_client_creation_response(
                        proposed_client_name=client_name,
                    )

                return await self._handle_client_name_input(
                    client_name, called_from=ConversationMessageProcessor._user_confirmation.__name__
                )

            elif (
                str(conversation.State) == ConversationState.COLLECTING_CLIENT_NAME.value
                and confirmed_data.client_name is None
                and not aggregated.client_name
                and confirmed_data.proposed_client_name
            ):
                # Confirm
                return await self._handle_client_creation_response(
                    proposed_client_name=confirmed_data.proposed_client_name
                )

            elif (
                str(conversation.State) == ConversationState.COLLECTING_COUNTRY.value
                and confirmed_data.ldmf_country is None
                and aggregated.ldmf_country[0]
            ):
                ldmf_country = aggregated.ldmf_country[0]
                return await self._handle_country_input(ldmf_country)

            elif (
                str(conversation.State) == ConversationState.COLLECTING_OBJECTIVE.value
                and confirmed_data.objective_and_scope is None
                and aggregated.objective_and_scope
            ):
                return await self._handle_objective_and_scope_input(aggregated.objective_and_scope)

            elif (
                str(conversation.State) == ConversationState.COLLECTING_OUTCOMES.value
                and confirmed_data.outcomes is None
                and aggregated.outcomes
            ):
                return await self._handle_outcomes_input(aggregated.outcomes)

            # Handle user response when in DATA_COMPLETE state (after showing CONFIRMED_FIELDS_READY)
            elif str(conversation.State) == ConversationState.DATA_COMPLETE.value and user_message:
                return await self._handle_data_complete_response(user_message, confirmed_data)

            reply_type = SystemReplyType.FIELD_SAVED
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.DATA_COMPLETE,
            )
        except Exception as e:
            logger.error('Exception in _extract_data method for conversation %s: %s', self.conversation_id, e)
            # Fallback to original behavior on any error
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )

    async def _user_denial(self, aggregated_data) -> ExtractionProcessingResult:
        # user_message = self._cleaned_user_message
        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(self.conversation_id))

            state_handlers = {
                ConversationState.INITIAL: self._dash_discard,
                ConversationState.COLLECTING_CLIENT_NAME: self._provide_client_name,
                ConversationState.COLLECTING_COUNTRY: self._handle_ldmf_denial,
                ConversationState.DATA_COMPLETE: self._generate_qual,
            }

            current_state = ConversationState(conversation.State)
            handler = state_handlers.get(current_state)

            if handler:
                return await handler()
            else:
                return self._uncertainty(aggregated_data)

        except Exception as e:
            logger.error('Exception in _user_denial method for conversation %s: %s', self.conversation_id, e)
            # Fallback to original behavior on any error
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )

    async def _change_engagement_dates(self) -> ExtractionProcessingResult:
        user_message = self._cleaned_user_message

        conversation = await self.conversation_repository.get(self.conversation_id)
        if not conversation:
            raise EntityNotFoundError('Conversation', str(self.conversation_id))

        confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
        if not confirmed_data.date_intervals:
            await self.conversation_repository.update_state(self.conversation_id, ConversationState.COLLECTING_DATES)

        date_option, sure = await self._analyze_dates_message(user_message)
        analyzed_start_date = date_option.start_date.isoformat() if date_option.start_date else None
        analyzed_end_date = date_option.end_date.isoformat() if date_option.end_date else None
        reply_type = SystemReplyType.DATES_UNAMBIGUOUS if sure else SystemReplyType.DATES_AMBIGUOUS
        confirmation_message = reply_type.message_text

        if confirmed_data.date_intervals:
            start_date, end_date = confirmed_data.date_intervals
            if analyzed_start_date or analyzed_end_date:
                start_date = analyzed_start_date if analyzed_start_date else start_date
                end_date = analyzed_end_date if analyzed_end_date else end_date

                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_DATES,
                    options=[(start_date, end_date)],
                )
            else:
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_DATES,
                    options=[(start_date, end_date)],
                )
        else:
            return ExtractionProcessingResult(
                system_reply=confirmation_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.COLLECTING_DATES,
                options=[(analyzed_start_date, analyzed_end_date)],
            )

    async def _handle_manual_input(
        self,
        conversation_id: UUID,
        conversation_state: ConversationState,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        message: str,
    ) -> bool:
        """
        Handle manual input from user. For now it only handles client name.
        This should not be called with many client name options.

        Args:
            conversation_id: The conversation ID
            conversation_state: The current state of the conversation
            aggregated_data: The aggregated data from all sources
            message: The user's message

        Returns:
            True if client name was confirmed and state updated, False otherwise.
        """
        states_for_client_name = [
            ConversationState.COLLECTING_CLIENT_NAME,
            ConversationState.INITIAL,
        ]

        # Only proceed if we're in a state expecting client name confirmation
        if conversation_state not in states_for_client_name or not aggregated_data.client_name:
            return False

        single_client_name = aggregated_data.client_name[0]
        cap_message = message.capitalize()
        prompt_is_in_confirmation = cap_message in SuggestedUserPrompt.agreement_prompts()

        # Check if the user's message matches any suggested client name or confirmation prompt
        if message == single_client_name or prompt_is_in_confirmation:
            # Determine the client name to confirm based on whether it was an agreement prompt
            # or a direct match to the single client name.
            possible_client_name = single_client_name if prompt_is_in_confirmation else message
            confirmed_client_name = confirmed_data.proposed_client_name or possible_client_name

            created = await self.extracted_data_service.get_or_create_client(confirmed_client_name, self.token)
            if not created:
                logger.info('Client %s already exists', confirmed_client_name)
            else:
                logger.info('Created client %s', confirmed_client_name)

            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name=str(ConfirmedDataFields.CLIENT_NAME),
                field_value=confirmed_client_name,
                state=ConversationState.COLLECTING_COUNTRY,
            )
            return True

        return False

    async def _provide_client_name(self) -> ExtractionProcessingResult:
        reply_type = SystemReplyType.PROVIDE_CLIENT_NAME_UNSURE
        return ExtractionProcessingResult(
            system_reply=reply_type.message_text,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
        )

    async def _handle_ldmf_denial(self) -> ExtractionProcessingResult:
        """
        Handle LDMF denial when user says "No" to LDMF confirmation.
        This method asks the user to provide the correct LDMF country.
        """
        reply_type = SystemReplyType.EXTRACTED_LDMF_NOT_VALID
        return ExtractionProcessingResult(
            system_reply=reply_type.message_text,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.EXTRACTED_DATA_NOT_VALID,
            conversation_state=ConversationState.COLLECTING_COUNTRY,
        )

    async def _manual_ldmf_input(self) -> ExtractionProcessingResult:
        """
        Handle manual LDMF country input from the user.
        This method processes the user's input and validates it against the LDMF API.
        """
        user_message = self._cleaned_user_message
        return await self._handle_country_input(user_message)

    async def _send_comprehensive_extraction_queue_message(self) -> None:
        """
        Send a queue message with filtered message IDs for comprehensive extraction.

        Collects all messages from the conversation that match ANY of these criteria:
        - Message type is "file"
        - Message type is "text_with_file"
        - Message type is "text" AND has intent classification of "extraction"

        Creates and sends a QualQueueMessage with source=message_ids and the collected message IDs.
        """
        try:
            # Use the new repository method to get relevant messages
            filtered_message_ids = await self.conversation_message_repository.get_messages_for_comprehensive_extraction(
                self.conversation_id
            )
            logger.info(f'Filtered {len(filtered_message_ids)} messages for comprehensive extraction')

            if not filtered_message_ids:  # this should not be at this state
                return

            if not self.conversation_data:
                logger.warning(f'Conversation data not found for conversation {self.conversation_id}')
                return

            tense = self.conversation_data.confirmed_data.tense
            # Create queue message with message_ids and tense
            queue_message = QualQueueMessage(
                source=Source(message_ids=filtered_message_ids),
                signal_r_connection_id=str(self.conversation_id),
                tense=tense,
            )
            # Send the queue message
            await self.document_service.document_queue_repository.send_message(queue_message)

            logger.info(
                f'Successfully sent comprehensive extraction queue message for conversation {self.conversation_id}'
            )

        except Exception:
            logger.exception(
                f'Error sending comprehensive extraction queue message for conversation {self.conversation_id}'
            )
