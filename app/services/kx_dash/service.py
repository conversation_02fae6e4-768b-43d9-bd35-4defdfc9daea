from datetime import date, datetime
import logging
from typing import Any, Sequence
from uuid import UUID

from constants.extracted_data import DataSourceType
from constants.message import MessageRole, MessageType
from exceptions import EntityNotFoundError
from repositories import ConversationRepository, KXDashRepository
from schemas import DashTaskResponse, KXDash<PERSON>askOption, MessageValidator
from services.date_validator import DateValidatorService

from ..extracted_data import ExtractedDataService
from .formatter import kx_dash_message_formatter


__all__ = ['KXDashService']


logger = logging.getLogger(__name__)


class KXDashService:
    """Service for KX Dash API interactions and business logic."""

    _DASH_TASK_STATUS_NOT_STARTED = 'Not Started'
    _DASH_TASK_ACTIVITY_TYPE_CONTRIBUTE_QUAL = 'Contribute qual'
    _VALIDATION_DATE_MESSAGE = (
        'Validate these engagement dates from KX Dash API: {engagementStartDate} - {engagementEndDate}'
    )

    _ATTRIBUTES_TO_EXTRACT = (
        'activity_id',
        'client_name',
        'activity_name',
        'member_firm',
        'country',
        'engagement_start_date',
        'engagement_end_date',
        'engagement_start_date_original',
        'engagement_end_date_original',
        'engagement_lep_emails',
        'engagement_manager_emails',
        'global_business',
        'global_business_service_area',
        'global_business_service_line',
        'global_industry',
        'global_industry_sector',
        'global_lcsp_emails',
    )

    def __init__(
        self,
        kx_dash_repository: KXDashRepository,
        conversation_repository: ConversationRepository,
        extracted_data_service: ExtractedDataService,
        date_validator_service: DateValidatorService,
    ):
        """
        Initialize the KX Dash Service with repositories.

        Args:
            kx_dash_repository: Repository for KX Dash API operations
            conversation_repository: Repository for conversation operations
            extracted_data_service: Service for extracted data operations
            date_validator_service: Service for date validation
        """
        self.kx_dash_repository = kx_dash_repository
        self.conversation_repository = conversation_repository
        self.extracted_data_service = extracted_data_service
        self._date_validator_service = date_validator_service

    async def list(self, token: str) -> Sequence[DashTaskResponse]:
        """
        Get a list of activities matching the query.

        Returns:
            List of DashTaskResponse objects

        Raises:
            Exception: If there's an error fetching activities
        """
        try:
            logger.debug('Fetching activities')
            result = await self.kx_dash_repository.list(token)
            sorted_result = sorted(
                result,
                key=lambda x: (x['dueDate'] is None, datetime.fromisoformat(x['dueDate']) if x['dueDate'] else None),
            )

            # Validate engagement dates with LLM if needed
            for activity in sorted_result:
                if activity[self.kx_dash_repository._NEED_LLM_DATE_VALIDATION_KEY]:
                    start_date, end_date = await self._analyze_engagement_dates(
                        self._VALIDATION_DATE_MESSAGE.format(
                            engagementStartDate=activity['engagementStartDate'],
                            engagementEndDate=activity['engagementEndDate'],
                        )
                    )
                    activity['engagementStartDate'], activity['engagementEndDate'] = start_date, end_date

            if not result:
                return ()

            logger.debug('Filtering out used KX Dash task IDs')
            return tuple(
                DashTaskResponse.model_validate(activity)
                for activity in sorted_result
                if activity['status'] == self._DASH_TASK_STATUS_NOT_STARTED
                and activity['activityType'] == self._DASH_TASK_ACTIVITY_TYPE_CONTRIBUTE_QUAL
            )
        except Exception as e:  # pragma: no cover
            logger.error('Error fetching activities: %s', e)
            raise

    async def get(self, activity_id: int, token: str) -> DashTaskResponse:
        """
        Get an activity by its ID.

        Args:
            activity_id: The ID of the activity

        Returns:
            DashTaskResponse object

        Raises:
            EntityNotFoundError: If the activity doesn't exist
            Exception: If there's an error fetching the activity
        """
        try:
            logger.debug('Fetching activity with ID: %s', activity_id)
            result = await self.kx_dash_repository.get(activity_id, token)

            if not result:
                raise EntityNotFoundError('Activity', str(activity_id))
            if result[self.kx_dash_repository._NEED_LLM_DATE_VALIDATION_KEY]:
                start_date, end_date = await self._analyze_engagement_dates(
                    self._VALIDATION_DATE_MESSAGE.format(
                        engagementStartDate=result['engagementStartDate'],
                        engagementEndDate=result['engagementEndDate'],
                    )
                )
                result['engagementStartDate'], result['engagementEndDate'] = start_date, end_date

            return DashTaskResponse.model_validate(result)
        except EntityNotFoundError:
            raise
        except Exception as e:  # pragma: no cover
            logger.error('Error fetching activity: %s', e)
            raise

    async def on_select(
        self,
        selected_option: KXDashTaskOption,
        conversation_id: UUID,
        token: str = '',
    ) -> MessageValidator:
        """
        Select an activity and create a system message in the conversation.

        Args:
            selected_option: ...
            conversation_id: The ID of the conversation
            is_welcome_message: Whether this is a welcome message

        Returns:
            Response with conversation, message, and activity data

        Raises:
            EntityNotFoundError: If the activity or conversation doesn't exist
            Exception: If there's an error selecting the activity
        """
        try:
            if not selected_option.activity_id:
                raise EntityNotFoundError('Activity', str(selected_option.activity_id))

            activity = await self.get(selected_option.activity_id, token)
            activity_details = self._extract_activity_details(activity)

            # Update the conversation's dash_activity_id
            await self.conversation_repository.update_dash_activity_id(conversation_id, selected_option.activity_id)

            await self.extracted_data_service.update(
                conversation_id=conversation_id,
                raw_data=activity_details,
                source_type=DataSourceType.KX_DASH,
                token=token,
            )
            reply_type, data = kx_dash_message_formatter(activity_details)
            content = reply_type.message_text.format(**data)
            message_data = MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=content,
                system_reply_type=reply_type,
                selected_option=selected_option,
            )
            return message_data

        except EntityNotFoundError:
            raise

        except Exception as e:  # pragma: no cover
            logger.error('Error selecting activity: %s', e)
            raise

    async def _analyze_engagement_dates(self, message: str) -> tuple[date | None, date | None]:
        """
        Process message content and determine system reply.

        Args:
            message: The user message to analyze

        Returns:
            Tuple with date option and sure flag
        """
        date_validator_response = await self._date_validator_service.find_dates(user_message=message)

        start_date = date_validator_response.date_1
        end_date = date_validator_response.date_2

        # Sort dates if both are present
        if start_date and end_date and end_date < start_date:
            start_date, end_date = end_date, start_date

        return start_date, end_date

    async def _exclude_unsure_engagement_date(self, date: date | None, date_original: str):
        if (
            date
            and date.day <= 12
            and not (await self._date_validator_service.date_is_text(user_message=date_original, date=date.isoformat()))
        ):
            return None

        return date

    @classmethod
    def _extract_activity_details(cls, activity: DashTaskResponse) -> dict[str, Any]:
        """
        Extract the details from the dash task response.

        Args:
            activity: The dash task response object

        Returns:
            Dictionary with extracted attributes
        """
        activity_data = activity.model_dump(exclude_none=True)
        return {attr: val for attr in cls._ATTRIBUTES_TO_EXTRACT if (val := activity_data.get(attr)) is not None}
